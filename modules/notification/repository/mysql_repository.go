package repository

import (
	"fmt"

	"wnapi/internal/pkg/logger"
	"wnapi/modules/notification/internal"
	"wnapi/modules/notification/repository/mysql"

	"github.com/jmoiron/sqlx"
)

// MySQLRepository kết hợp tất cả các repository MySQL của module notification
type MySQLRepository struct {
	notificationRepository *mysql.NotificationRepository
	templateRepository     *mysql.TemplateRepository
	channelRepository      ChannelRepository
	preferenceRepository   *mysql.PreferenceRepository
	deliveryRepository     *mysql.DeliveryRepository
	telegramRepository     *mysql.TelegramRepository
	websocketRepository    *mysql.WebsocketRepository
}

// NewMySQLRepository tạo một repository MySQL mới
func NewMySQLRepository(db *sqlx.DB, logger logger.Logger) (internal.Repository, error) {
	if db == nil {
		return nil, fmt.Errorf("database connection không được để trống")
	}

	return &MySQLRepository{
		notificationRepository: mysql.NewNotificationRepository(db, logger),
		channelRepository:      mysql.NewChannelRepository(db),
		// Phần còn lại sẽ được thêm sau khi các repository khác được triển khai
	}, nil
}

// Notification trả về repository cho notification
func (r *MySQLRepository) Notification() internal.NotificationRepository {
	return r.notificationRepository
}

// Template trả về repository cho template
func (r *MySQLRepository) Template() internal.TemplateRepository {
	// Placeholder, sẽ được triển khai sau
	return nil
}

// Channel trả về repository cho channel
func (r *MySQLRepository) Channel() internal.ChannelRepository {
	// Placeholder, sẽ được triển khai sau
	return nil
}

// Preference trả về repository cho preference
func (r *MySQLRepository) Preference() internal.PreferenceRepository {
	// Placeholder, sẽ được triển khai sau
	return nil
}

// Delivery trả về repository cho delivery
func (r *MySQLRepository) Delivery() internal.DeliveryRepository {
	// Placeholder, sẽ được triển khai sau
	return nil
}

// Telegram trả về repository cho telegram
func (r *MySQLRepository) Telegram() internal.TelegramRepository {
	// Placeholder, sẽ được triển khai sau
	return nil
}

// Websocket trả về repository cho websocket
func (r *MySQLRepository) Websocket() internal.WebsocketRepository {
	// Placeholder, sẽ được triển khai sau
	return nil
}
