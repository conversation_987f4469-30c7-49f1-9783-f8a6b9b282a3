package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"
	"wnapi/internal/core"
	"wnapi/internal/pkg/tracing"

	// Import modules để đăng ký với registry
	_ "wnapi/modules/auth"
	_ "wnapi/modules/hello"

	//_ "wnapi/modules/notification"
	_ "wnapi/modules/product"
	_ "wnapi/modules/rbac"

	// Import plugins để đăng ký với registry
	_ "wnapi/plugins/logger"
)

var (
	configFlag  = flag.String("config", ".env", "Path to .env file")
	projectFlag = flag.String("project", "", "Project to run")
	debugFlag   = flag.Bool("debug", false, "Enable debug mode")
)

func main() {
	flag.Parse()

	// Initialize tracing
	if err := tracing.InitializeGlobal(); err != nil {
		log.Printf("Warning: Failed to initialize tracing: %v", err)
	} else {
		log.Println("Tracing initialized successfully")
	}

	// Tạo App Options
	options := core.AppOptions{
		ConfigPath:  *configFlag,
		ProjectName: *projectFlag,
	}

	// Khởi tạo ứng dụng
	app, err := core.NewApp(options)
	if err != nil {
		log.Fatalf("Error initializing app: %v", err)
	}

	// Đăng ký signal handlers
	signalCh := make(chan os.Signal, 1)
	signal.Notify(signalCh, os.Interrupt, syscall.SIGTERM)
	go func() {
		sig := <-signalCh
		log.Printf("Received signal %v, shutting down...", sig)

		// Shutdown tracing with timeout
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := tracing.ShutdownGlobal(ctx); err != nil {
			log.Printf("Error shutting down tracing: %v", err)
		}

		if err := app.Shutdown(); err != nil {
			log.Printf("Error during shutdown: %v", err)
		}
		os.Exit(0)
	}()

	// Khởi tạo ứng dụng
	if err := app.Initialize(); err != nil {
		log.Fatalf("Error initializing app: %v", err)
	}

	// Hiển thị thông tin về các module đã nạp
	printModuleInfo(app)

	// Khởi động ứng dụng
	if err := app.Start(); err != nil {
		log.Fatalf("Error starting app: %v", err)
	}

	fmt.Printf("Server started at %s\n", app.GetServerAddress())
	fmt.Println("Press Ctrl+C to stop the server")

	// Chờ cho đến khi quá trình hoàn tất
	select {}
}

// printModuleInfo hiển thị thông tin về các module đã nạp
func printModuleInfo(app *core.App) {
	modules := app.GetModules()
	if len(modules) == 0 {
		fmt.Println("No modules loaded")
		return
	}

	fmt.Printf("\n=== Loaded Modules (%d) ===\n", len(modules))
	for _, module := range modules {
		fmt.Printf("- %s\n", module.Name())
	}

	// Hiển thị các plugin đã nạp
	plugins := app.GetPlugins()
	if len(plugins) > 0 {
		fmt.Printf("\n=== Loaded Plugins (%d) ===\n", len(plugins))
		for _, plugin := range plugins {
			fmt.Printf("- %s\n", plugin.Name())
		}
	}
}
