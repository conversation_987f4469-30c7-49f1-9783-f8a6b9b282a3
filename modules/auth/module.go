package auth

import (
	"context"
	"path/filepath"
	"time"

	"wnapi/internal/core"
	pkgauth "wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/auth/api"
	"wnapi/modules/auth/internal"
	"wnapi/modules/auth/models"
	"wnapi/modules/auth/repository"
	"wnapi/modules/auth/repository/mysql"
	"wnapi/modules/auth/service"
)

// mockPasswordResetRepository là một triển khai giả của PasswordResetRepository
type mockPasswordResetRepository struct{}

func (m *mockPasswordResetRepository) Create(ctx context.Context, email string, token string, expiresAt time.Time) error {
	return nil
}

func (m *mockPasswordResetRepository) FindByToken(ctx context.Context, token string) (*models.PasswordReset, error) {
	return &models.PasswordReset{
		Email:     "<EMAIL>",
		Token:     token,
		Used:      false,
		ExpiresAt: time.Now().Add(24 * time.Hour),
	}, nil
}

func (m *mockPasswordResetRepository) MarkAsUsed(ctx context.Context, token string) error {
	return nil
}

func (m *mockPasswordResetRepository) InvalidateOldTokens(ctx context.Context, email string) error {
	return nil
}

func (m *mockPasswordResetRepository) DeleteExpiredTokens(ctx context.Context) error {
	return nil
}

func init() {
	core.RegisterModuleFactory("auth", NewModule)
}

// Module triển khai auth module
type Module struct {
	name    string
	logger  logger.Logger
	config  map[string]interface{}
	app     *core.App
	handler *api.Handler
}

// NewModule tạo module mới
func NewModule(app *core.App, config map[string]interface{}) (core.Module, error) {
	logger := app.GetLogger()

	// Khởi tạo repository
	repo, err := mysql.NewMySQLRepository(app.GetDBManager(), logger)
	if err != nil {
		return nil, err
	}

	// Lấy kết nối database để tạo repository cho password reset
	var passwordResetRepo repository.PasswordResetRepository

	// Sử dụng mock repository vì chúng ta không thể lấy trực tiếp *sql.DB từ DBManager
	passwordResetRepo = &mockPasswordResetRepository{}
	logger.Info("Sử dụng mock password reset repository")

	// Đọc cấu hình từ biến môi trường
	authConfig, err := internal.LoadAuthConfig()
	if err != nil {
		logger.Warn("Không thể đọc cấu hình từ biến môi trường, sử dụng giá trị mặc định: %v", err)

		// Khởi tạo cấu hình mặc định nếu không đọc được từ env
		authConfig = &internal.AuthConfig{
			JWTSecret:          "default_jwt_secret_change_me_in_production",
			AccessTokenExpiry:  15 * time.Minute,
			RefreshTokenExpiry: 168 * time.Hour,
			Message:            "Xin chào từ module Auth!",
		}
	}

	// Ghi đè cấu hình từ config map nếu có (để tương thích ngược)
	if jwtSecret, ok := config["jwt_secret"].(string); ok && jwtSecret != "" {
		authConfig.JWTSecret = jwtSecret
	}

	if accessExpiry, ok := config["access_token_expiry"].(string); ok && accessExpiry != "" {
		if duration, err := time.ParseDuration(accessExpiry); err == nil {
			authConfig.AccessTokenExpiry = duration
		}
	}

	if refreshExpiry, ok := config["refresh_token_expiry"].(string); ok && refreshExpiry != "" {
		if duration, err := time.ParseDuration(refreshExpiry); err == nil {
			authConfig.RefreshTokenExpiry = duration
		}
	}

	// Log thông tin cấu hình (che giấu secret)
	logger.Info("Cấu hình Auth: AccessTokenExpiry=%v, RefreshTokenExpiry=%v",
		authConfig.AccessTokenExpiry, authConfig.RefreshTokenExpiry)

	// Tạo AuthService từ service package
	authService := service.NewService(repo, *authConfig, logger)

	// Tạo PasswordResetService
	baseURL := "http://localhost:8080" // Giả sử URL API
	webURL := "http://localhost:3000"  // Giả sử URL frontend

	// Trong triển khai thực tế, userConn và notificationConn sẽ được khởi tạo từ gRPC client
	// Tạm thời sử dụng nil để tránh lỗi import
	passwordResetService := service.NewPasswordResetService(
		passwordResetRepo,
		nil, // userConn
		nil, // notificationConn
		baseURL,
		webURL,
	)

	// Tạo JWTConfig từ AuthConfig
	jwtConfig := pkgauth.JWTConfig{
		AccessSigningKey:       authConfig.JWTSecret,
		RefreshSigningKey:      authConfig.JWTSecret,
		AccessTokenExpiration:  authConfig.AccessTokenExpiry,
		RefreshTokenExpiration: authConfig.RefreshTokenExpiry,
		Issuer:                 "wnapi-auth-module",
	}

	// Tạo handler với AuthService, PasswordResetService và JWTConfig
	handler := api.NewHandler(authService, passwordResetService, jwtConfig)

	return &Module{
		name:    "auth",
		logger:  logger,
		config:  config,
		app:     app,
		handler: handler,
	}, nil
}

// Name trả về tên của module
func (m *Module) Name() string {
	return m.name
}

// Init khởi tạo module
func (m *Module) Init(ctx context.Context) error {
	m.logger.Info("Initializing auth module")
	return nil
}

// RegisterRoutes đăng ký các route của module
func (m *Module) RegisterRoutes(server *core.Server) error {
	if m.handler == nil {
		m.logger.Warn("Auth handler is not initialized, skipping route registration")
		return nil
	}

	err := registerRoutes(server, m.handler)
	if err != nil {
		return err
	}

	return nil
}

// Cleanup dọn dẹp tài nguyên của module
func (m *Module) Cleanup(ctx context.Context) error {
	m.logger.Info("Cleaning up auth module")
	return nil
}

// GetMigrationPath trả về đường dẫn chứa migrations
func (m *Module) GetMigrationPath() string {
	return filepath.Join("modules", "auth", "migrations")
}

// GetMigrationOrder trả về thứ tự ưu tiên khi chạy migration của module
func (m *Module) GetMigrationOrder() int {
	return 1 // Auth module cần chạy đầu tiên
}
